[ModulePackage] Module execution failed for lexflow: Error: Failed to execute module code: Invalid or unexpected token
    at ModulePackage.createUnsandboxedModuleClass (ModulePackage.js:504:27)
    at ModulePackage.createModuleClass (ModulePackage.js:369:41)
    at ModulePackage.initialize (ModulePackage.js:139:28)
    at DynamicModuleManager.installModulePackage (DynamicModuleManager.js:158:33)
    at async CoreDeskApp.handleModuleInstall (app.js:1177:17)
    at async HTMLButtonElement.<anonymous> (app.js:997:17)
error @ ModulePackage.js:57
createUnsandboxedModuleClass @ ModulePackage.js:518
createModuleClass @ ModulePackage.js:369
initialize @ ModulePackage.js:139
installModulePackage @ DynamicModuleManager.js:158
await in installModulePackage (async)
handleModuleInstall @ app.js:1177
await in handleModuleInstall (async)
(anonymous) @ app.js:997
ModulePackage.js:57 [ModulePackage] Failed to create module class for lexflow: Error: Module execution failed: Failed to execute module code: Invalid or unexpected token
    at ModulePackage.createUnsandboxedModuleClass (ModulePackage.js:520:19)
    at ModulePackage.createModuleClass (ModulePackage.js:369:41)
    at ModulePackage.initialize (ModulePackage.js:139:28)
    at DynamicModuleManager.installModulePackage (DynamicModuleManager.js:158:33)
    at async CoreDeskApp.handleModuleInstall (app.js:1177:17)
    at async HTMLButtonElement.<anonymous> (app.js:997:17)
error @ ModulePackage.js:57
createModuleClass @ ModulePackage.js:380
initialize @ ModulePackage.js:139
installModulePackage @ DynamicModuleManager.js:158
await in installModulePackage (async)
handleModuleInstall @ app.js:1177
await in handleModuleInstall (async)
(anonymous) @ app.js:997
ModulePackage.js:57 [ModulePackage] Failed to initialize package lexflow: Error: Invalid module code: Module execution failed: Failed to execute module code: Invalid or unexpected token
    at ModulePackage.createModuleClass (ModulePackage.js:381:19)
    at ModulePackage.initialize (ModulePackage.js:139:28)
    at DynamicModuleManager.installModulePackage (DynamicModuleManager.js:158:33)
    at async CoreDeskApp.handleModuleInstall (app.js:1177:17)
    at async HTMLButtonElement.<anonymous> (app.js:997:17)
error @ ModulePackage.js:57
initialize @ ModulePackage.js:149
await in initialize (async)
installModulePackage @ DynamicModuleManager.js:158
await in installModulePackage (async)
handleModuleInstall @ app.js:1177
await in handleModuleInstall (async)
(anonymous) @ app.js:997
DynamicModuleManager.js:47 [DynamicModuleManager] Failed to install module package: Error: Invalid module code: Module execution failed: Failed to execute module code: Invalid or unexpected token
    at ModulePackage.createModuleClass (ModulePackage.js:381:19)
    at ModulePackage.initialize (ModulePackage.js:139:28)
    at DynamicModuleManager.installModulePackage (DynamicModuleManager.js:158:33)
    at async CoreDeskApp.handleModuleInstall (app.js:1177:17)
    at async HTMLButtonElement.<anonymous> (app.js:997:17)
error @ DynamicModuleManager.js:47
installModulePackage @ DynamicModuleManager.js:184
await in installModulePackage (async)
handleModuleInstall @ app.js:1177
await in handleModuleInstall (async)
(anonymous) @ app.js:997
app.js:15 [CoreDeskApp] Failed to install module lexflow: Error: Invalid module code: Module execution failed: Failed to execute module code: Invalid or unexpected token
    at ModulePackage.createModuleClass (ModulePackage.js:381:19)
    at ModulePackage.initialize (ModulePackage.js:139:28)
    at DynamicModuleManager.installModulePackage (DynamicModuleManager.js:158:33)
    at async CoreDeskApp.handleModuleInstall (app.js:1177:17)
    at async HTMLButtonElement.<anonymous> (app.js:997:17)
error @ app.js:15
handleModuleInstall @ app.js:1209
await in handleModuleInstall (async)
(anonymous) @ app.js:997