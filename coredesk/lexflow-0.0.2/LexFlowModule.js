/**
 * LexFlow Module - Complete Legal Case Management System
 * Implements full functionality for case management, documents, events, and notes
 * as specified in CoreDesk Framework v2.0.0 PRD
 */

class LexFlowModule {
    constructor() {
        this.moduleCode = 'lexflow';
        this.moduleName = 'LexFlow';
        this.moduleDescription = 'Gestión completa de casos legales y documentación jurídica';
        this.isInitialized = false;
        this.isActive = false;
        
        // UI Components
        this.container = null;
        this.sidebar = null;
        this.mainContent = null;
        this.currentView = 'dashboard'; // dashboard, cases, case-detail, documents, events
        this.selectedCase = null;
        
        // Data Management
        this.cases = [];
        this.documents = [];
        this.events = [];
        this.notes = [];
        
        // UI State
        this.searchQuery = '';
        this.filterStatus = 'all';
        this.filterPriority = 'all';
        
        this.initialize();
    }

    /**
     * Initialize the LexFlow module
     */
    async initialize() {
        try {
            console.log('Module', '[LexFlow] Initializing...', );
            
            await this.loadData();
            this.createUIStructure();
            this.updateCounts(); // Update counts after UI is created
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('Module', '[LexFlow] Initialized successfully', );
            
        } catch (error) {
            console.error('Module', '[LexFlow] Initialization failed:', error);
            throw error;
        }
    }

    /**
     * Activate the module
     */
    async activate() {
        try {
            console.log('Module', '[LexFlow] Starting activation...', );
            
            if (!this.isInitialized) {
                console.log('Module', '[LexFlow] Not initialized, initializing now...', );
                await this.initialize();
            }
            
            console.log('Module', '[LexFlow] Activating...', );
            
            // Ensure container exists (recreate if needed after deactivation)
            if (!this.container) {
                console.log('Module', '[LexFlow] No container found, creating UI structure...', );
                this.createUIStructure();
                this.setupEventListeners(); // Re-setup event listeners for new DOM
            }
            
            // Show module container
            if (this.container) {
                this.container.style.display = 'flex';
                console.log('Module', '[LexFlow] Container made visible', );
            } else {
                console.error('Module', '[LexFlow] Container still not found after creation!', );
                return;
            }
            
            // Refresh data
            await this.refreshData();
            
            // Set initial view
            this.showView('dashboard');
            
            this.isActive = true;
            
            // Update activity bar
            window.activityBar?.updateActiveModule(this.moduleCode);
            
            // Update status bar
            window.statusBar?.updateModuleStatus(this.moduleCode);
            
            console.log('Module', '[LexFlow] Activated successfully', );
            
        } catch (error) {
            console.error('Module', '[LexFlow] Activation failed:', error);
            throw error;
        }
    }

    /**
     * Deactivate the module
     */
    async deactivate() {
        console.log('Module', '[LexFlow] Deactivating...', );
        
        // Save current state
        this.saveState();
        
        // Remove module container completely
        if (this.container) {
            this.container.remove();
            this.container = null;
        }
        
        // Show welcome screen if no other modules are active
        const welcomeScreen = document.querySelector('#welcome-screen');
        if (welcomeScreen) {
            welcomeScreen.style.display = 'flex';
        }
        
        this.isActive = false;
        
        console.log('Module', '[LexFlow] Deactivated successfully', );
    }

    /**
     * Create the UI structure
     */
    createUIStructure() {
        // Create main container
        this.container = document.createElement('div');
        this.container.className = 'lexflow-module';
        this.container.id = 'lexflow-container';
        this.container.style.display = 'none';
        
        this.container.innerHTML = `
            <div class="lexflow-layout">
                <!-- Sidebar Navigation -->
                <div class="lexflow-sidebar" id="lexflow-sidebar">
                    <div class="sidebar-header">
                        <h2>LexFlow</h2>
                        <button class="btn btn-primary btn-sm" id="new-case-btn">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M19,*************************************"/>
                            </svg>
                            Nuevo Caso
                        </button>
                    </div>
                    
                    <div class="sidebar-menu">
                        <div class="menu-item active" data-view="dashboard">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M13,3V9H21V3M13,21H21V11H13M3,21H11V15H3M3,13H11V3H3V13Z"/>
                            </svg>
                            Dashboard
                        </div>
                        <div class="menu-item" data-view="cases">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                            </svg>
                            Casos
                            <span class="count" id="cases-count">0</span>
                        </div>
                        <div class="menu-item" data-view="documents">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                            </svg>
                            Documentos
                            <span class="count" id="documents-count">0</span>
                        </div>
                        <div class="menu-item" data-view="events">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M19,3H18V1H16V3H8V1H6V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V8H19V19Z"/>
                            </svg>
                            Eventos y Plazos
                            <span class="count" id="events-count">0</span>
                        </div>
                    </div>
                    
                    <div class="sidebar-stats">
                        <div class="stat-item">
                            <span class="stat-label">Casos Activos</span>
                            <span class="stat-value" id="active-cases-stat">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Próximos Vencimientos</span>
                            <span class="stat-value" id="upcoming-events-stat">0</span>
                        </div>
                    </div>
                </div>
                
                <!-- Main Content Area -->
                <div class="lexflow-main" id="lexflow-main">
                    <div class="main-header">
                        <div class="header-title">
                            <h1 id="view-title">Dashboard</h1>
                            <p id="view-subtitle">Resumen general de actividad legal</p>
                        </div>
                        <div class="header-actions" id="header-actions">
                            <!-- Dynamic action buttons will be inserted here -->
                        </div>
                    </div>
                    
                    <div class="main-content" id="main-content">
                        <!-- Dynamic content will be inserted here -->
                    </div>
                </div>
            </div>
        `;
        
        // Add to main container WITHOUT clearing existing content
        const mainContainer = document.querySelector('#tab-content') || document.body;
        
        // Hide welcome screen if it exists
        const welcomeScreen = document.querySelector('#welcome-screen');
        if (welcomeScreen) {
            welcomeScreen.style.display = 'none';
        }
        
        // Remove any existing lexflow containers
        const existingContainer = document.querySelector('#lexflow-container');
        if (existingContainer) {
            existingContainer.remove();
        }
        
        // Append the container to tab-content
        mainContainer.appendChild(this.container);
        
        // Store references
        this.sidebar = this.container.querySelector('#lexflow-sidebar');
        this.mainContent = this.container.querySelector('#main-content');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Sidebar navigation
        this.sidebar.addEventListener('click', (e) => {
            const menuItem = e.target.closest('.menu-item');
            if (menuItem) {
                const view = menuItem.dataset.view;
                this.showView(view);
            }
        });
        
        // New case button
        const newCaseBtn = this.container.querySelector('#new-case-btn');
        newCaseBtn.addEventListener('click', () => {
            this.showNewCaseForm();
        });
        
        // Global search handling
        document.addEventListener('keydown', (e) => {
            if (this.isActive && e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                this.focusSearch();
            }
        });
    }

    /**
     * Show specific view
     */
    showView(viewName) {
        if (this.currentView === viewName) return;
        
        // Update sidebar navigation
        this.sidebar.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.view === viewName) {
                item.classList.add('active');
            }
        });
        
        this.currentView = viewName;
        
        // Update header and content based on view
        switch (viewName) {
            case 'dashboard':
                this.showDashboard();
                break;
            case 'cases':
                this.showCasesView();
                break;
            case 'documents':
                this.showDocumentsView();
                break;
            case 'events':
                this.showEventsView();
                break;
            case 'case-detail':
                this.showCaseDetail(this.selectedCase);
                break;
            default:
                this.showDashboard();
        }
        
        console.log('Module', '[LexFlow] Switched to view:', viewName);
    }

    /**
     * Show dashboard view
     */
    showDashboard() {
        this.updateHeader('Dashboard', 'Resumen general de actividad legal');
        
        const dashboardHTML = `
            <div class="dashboard-content">
                <div class="dashboard-grid">
                    <!-- Quick Stats -->
                    <div class="dashboard-card stats-card">
                        <h3>Estadísticas Rápidas</h3>
                        <div class="stats-grid">
                            <div class="stat-box">
                                <div class="stat-number">${this.cases.filter(c => c.status === 'active').length}</div>
                                <div class="stat-label">Casos Activos</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number">${this.cases.filter(c => c.priority === 'urgent').length}</div>
                                <div class="stat-label">Casos Urgentes</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number">${this.documents.length}</div>
                                <div class="stat-label">Documentos</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number">${this.getUpcomingEvents().length}</div>
                                <div class="stat-label">Próximos Eventos</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Cases -->
                    <div class="dashboard-card recent-cases-card">
                        <h3>Casos Recientes</h3>
                        <div class="recent-cases-list">
                            ${this.getRecentCases().map(caseItem => `
                                <div class="case-item" data-case-id="${caseItem.id}">
                                    <div class="case-info">
                                        <div class="case-title">${caseItem.title}</div>
                                        <div class="case-client">${caseItem.client_name}</div>
                                        <div class="case-meta">
                                            <span class="case-number">#${caseItem.case_number}</span>
                                            <span class="case-status status-${caseItem.status}">${caseItem.status}</span>
                                            <span class="case-priority priority-${caseItem.priority}">${caseItem.priority}</span>
                                        </div>
                                    </div>
                                    <div class="case-actions">
                                        <button class="btn btn-sm" onclick="lexflowModule.openCase(${caseItem.id})">
                                            Abrir
                                        </button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                        ${this.cases.length === 0 ? '<p class="empty-state">No hay casos registrados</p>' : ''}
                    </div>
                    
                    <!-- Upcoming Events -->
                    <div class="dashboard-card upcoming-events-card">
                        <h3>Próximos Eventos</h3>
                        <div class="events-list">
                            ${this.getUpcomingEvents().map(event => `
                                <div class="event-item">
                                    <div class="event-date">
                                        <div class="date-day">${new Date(event.event_date).getDate()}</div>
                                        <div class="date-month">${new Date(event.event_date).toLocaleDateString('es-ES', { month: 'short' })}</div>
                                    </div>
                                    <div class="event-info">
                                        <div class="event-title">${event.title}</div>
                                        <div class="event-time">${new Date(event.event_date).toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' })}</div>
                                        <div class="event-case">Caso: ${this.getCaseById(event.case_id)?.title || 'Sin caso'}</div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                        ${this.getUpcomingEvents().length === 0 ? '<p class="empty-state">No hay eventos próximos</p>' : ''}
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="dashboard-card quick-actions-card">
                        <h3>Acciones Rápidas</h3>
                        <div class="quick-actions">
                            <button class="action-btn" onclick="lexflowModule.showNewCaseForm()">
                                <svg class="icon" viewBox="0 0 24 24">
                                    <path d="M19,*************************************"/>
                                </svg>
                                Nuevo Caso
                            </button>
                            <button class="action-btn" onclick="lexflowModule.showNewDocumentForm()">
                                <svg class="icon" viewBox="0 0 24 24">
                                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2Z"/>
                                </svg>
                                Subir Documento
                            </button>
                            <button class="action-btn" onclick="lexflowModule.showNewEventForm()">
                                <svg class="icon" viewBox="0 0 24 24">
                                    <path d="M19,3H18V1H16V3H8V1H6V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3Z"/>
                                </svg>
                                Agendar Evento
                            </button>
                            <button class="action-btn" onclick="lexflowModule.showView('cases')">
                                <svg class="icon" viewBox="0 0 24 24">
                                    <path d="M9,5V9H21V5M9,19H21V15H9M9,14H21V10H9M4,9H8L6,7L4,9M4,19H8L6,17L4,19M4,14H8L6,12L4,14Z"/>
                                </svg>
                                Ver Todos los Casos
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.mainContent.innerHTML = dashboardHTML;
        this.updateHeaderActions('dashboard');
    }

    /**
     * Show cases view
     */
    showCasesView() {
        this.updateHeader('Casos', 'Gestión de casos legales');
        
        const casesHTML = `
            <div class="cases-view">
                <div class="cases-toolbar">
                    <div class="search-controls">
                        <div class="search-box">
                            <input type="text" placeholder="Buscar casos..." id="cases-search" value="${this.searchQuery}">
                            <svg class="search-icon" viewBox="0 0 24 24">
                                <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L20.49,19.78L19.78,20.49L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,4A5.5,5.5 0 0,0 4,9.5A5.5,5.5 0 0,0 9.5,15A5.5,5.5 0 0,0 15,9.5A5.5,5.5 0 0,0 9.5,4Z"/>
                            </svg>
                        </div>
                        <select id="status-filter" class="filter-select">
                            <option value="all">Todos los estados</option>
                            <option value="active">Activos</option>
                            <option value="closed">Cerrados</option>
                            <option value="suspended">Suspendidos</option>
                        </select>
                        <select id="priority-filter" class="filter-select">
                            <option value="all">Todas las prioridades</option>
                            <option value="low">Baja</option>
                            <option value="normal">Normal</option>
                            <option value="high">Alta</option>
                            <option value="urgent">Urgente</option>
                        </select>
                    </div>
                    <div class="view-controls">
                        <button class="view-btn active" data-view="grid">
                            <svg viewBox="0 0 24 24"><path d="M3,11H11V3H3M3,21H11V13H3M13,21H21V13H13M13,3V11H21V3"/></svg>
                        </button>
                        <button class="view-btn" data-view="list">
                            <svg viewBox="0 0 24 24"><path d="M9,5V9H21V5M9,19H21V15H9M9,14H21V10H9M4,9H8L6,7L4,9M4,19H8L6,17L4,19M4,14H8L6,12L4,14Z"/></svg>
                        </button>
                    </div>
                </div>
                
                <div class="cases-content">
                    <div class="cases-grid" id="cases-grid">
                        ${this.getFilteredCases().map(caseItem => this.renderCaseCard(caseItem)).join('')}
                    </div>
                    ${this.getFilteredCases().length === 0 ? '<div class="empty-state"><p>No se encontraron casos</p></div>' : ''}
                </div>
            </div>
        `;
        
        this.mainContent.innerHTML = casesHTML;
        this.updateHeaderActions('cases');
        this.setupCasesEventListeners();
    }

    /**
     * Render case card
     */
    renderCaseCard(caseItem) {
        const caseDocuments = this.documents.filter(doc => doc.case_id === caseItem.id);
        const caseEvents = this.events.filter(event => event.case_id === caseItem.id);
        
        return `
            <div class="case-card" data-case-id="${caseItem.id}">
                <div class="case-header">
                    <div class="case-number">#${caseItem.case_number}</div>
                    <div class="case-status-badges">
                        <span class="status-badge status-${caseItem.status}">${caseItem.status}</span>
                        <span class="priority-badge priority-${caseItem.priority}">${caseItem.priority}</span>
                    </div>
                </div>
                <div class="case-body">
                    <h4 class="case-title">${caseItem.title}</h4>
                    <div class="case-client">
                        <svg class="icon" viewBox="0 0 24 24">
                            <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                        </svg>
                        ${caseItem.client_name}
                    </div>
                    <div class="case-type">${caseItem.case_type}</div>
                    ${caseItem.description ? `<div class="case-description">${caseItem.description.substring(0, 100)}...</div>` : ''}
                </div>
                <div class="case-footer">
                    <div class="case-stats">
                        <span class="stat-item">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2Z"/>
                            </svg>
                            ${caseDocuments.length}
                        </span>
                        <span class="stat-item">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M19,3H18V1H16V3H8V1H6V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3Z"/>
                            </svg>
                            ${caseEvents.length}
                        </span>
                    </div>
                    <div class="case-actions">
                        <button class="btn btn-sm btn-secondary" onclick="lexflowModule.editCase(${caseItem.id})">Editar</button>
                        <button class="btn btn-sm btn-primary" onclick="lexflowModule.openCase(${caseItem.id})">Abrir</button>
                    </div>
                </div>
                <div class="case-updated">Actualizado: ${new Date(caseItem.updated_at).toLocaleDateString('es-ES')}</div>
            </div>
        `;
    }

    /**
     * Setup cases view event listeners
     */
    setupCasesEventListeners() {
        // Search functionality
        const searchInput = this.container.querySelector('#cases-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value;
                this.refreshCasesView();
            });
        }
        
        // Filter functionality
        const statusFilter = this.container.querySelector('#status-filter');
        if (statusFilter) {
            statusFilter.value = this.filterStatus;
            statusFilter.addEventListener('change', (e) => {
                this.filterStatus = e.target.value;
                this.refreshCasesView();
            });
        }
        
        const priorityFilter = this.container.querySelector('#priority-filter');
        if (priorityFilter) {
            priorityFilter.value = this.filterPriority;
            priorityFilter.addEventListener('change', (e) => {
                this.filterPriority = e.target.value;
                this.refreshCasesView();
            });
        }
        
        // View toggle
        const viewButtons = this.container.querySelectorAll('.view-btn');
        viewButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                viewButtons.forEach(b => b.classList.remove('active'));
                e.target.closest('.view-btn').classList.add('active');
                
                const viewType = e.target.closest('.view-btn').dataset.view;
                this.toggleCasesView(viewType);
            });
        });
    }

    /**
     * Load data from database
     */
    async loadData() {
        try {
            console.log('Module', '[LexFlow] Loading data...', );
            
            if (window.databaseManager && window.databaseManager.isInitialized) {
                // Load cases
                this.cases = await window.databaseManager.all('SELECT * FROM lexflow_cases ORDER BY updated_at DESC');
                
                // Load documents
                this.documents = await window.databaseManager.all('SELECT * FROM lexflow_documents ORDER BY created_at DESC');
                
                // Load events
                this.events = await window.databaseManager.all('SELECT * FROM lexflow_events ORDER BY event_date ASC');
                
                // Load notes
                this.notes = await window.databaseManager.all('SELECT * FROM lexflow_notes ORDER BY created_at DESC');
                
                console.log('Module', '[LexFlow] Data loaded successfully', {
                    cases: this.cases.length,
                    documents: this.documents.length,
                    events: this.events.length,
                    notes: this.notes.length
                });
            } else {
                console.warn('Module', '[LexFlow] Database not available, using mock data', );
                this.loadMockData();
            }
            
        } catch (error) {
            console.error('Module', '[LexFlow] Error loading data:', error);
            this.loadMockData();
        }
    }

    /**
     * Load mock data for development/demo
     */
    loadMockData() {
        this.cases = [
            {
                id: 1,
                case_number: 'LEG-2024-001',
                title: 'Demanda Laboral - García vs. TechCorp',
                client_name: 'María García',
                client_email: '<EMAIL>',
                client_phone: '******-0123',
                case_type: 'Laboral',
                status: 'active',
                priority: 'high',
                description: 'Demanda por despido injustificado y pagos pendientes de horas extras.',
                created_at: '2024-06-15T10:00:00Z',
                updated_at: '2024-06-28T15:30:00Z'
            },
            {
                id: 2,
                case_number: 'LEG-2024-002',
                title: 'Contrato de Arrendamiento - Plaza Comercial',
                client_name: 'Inversiones del Norte S.A.',
                client_email: '<EMAIL>',
                client_phone: '******-0456',
                case_type: 'Comercial',
                status: 'active',
                priority: 'normal',
                description: 'Revisión y negociación de contrato de arrendamiento para plaza comercial.',
                created_at: '2024-06-20T14:00:00Z',
                updated_at: '2024-06-27T11:20:00Z'
            }
        ];
        
        this.documents = [
            {
                id: 1,
                case_id: 1,
                filename: 'demanda-inicial.pdf',
                file_type: 'pdf',
                file_size: 2048576,
                description: 'Demanda inicial presentada ante el tribunal',
                created_at: '2024-06-15T10:30:00Z'
            }
        ];
        
        this.events = [
            {
                id: 1,
                case_id: 1,
                event_type: 'hearing',
                title: 'Audiencia Preliminar',
                description: 'Primera audiencia del caso laboral',
                event_date: '2024-07-15T09:00:00Z',
                location: 'Tribunal Laboral - Sala 3',
                status: 'scheduled',
                created_at: '2024-06-15T10:45:00Z'
            }
        ];
        
        this.notes = [];
        
        // updateCounts will be called from loadData after UI is created
    }

    /**
     * Additional utility methods for LexFlow functionality
     */
    
    updateCounts() {
        // Check if container exists before trying to update counts
        if (!this.container) {
            console.warn('Module', '[LexFlow] Container not available for count updates');
            return;
        }
        
        // Update sidebar counts
        const elements = {
            'cases-count': this.cases.length,
            'documents-count': this.documents.length,
            'events-count': this.events.length,
            'active-cases-stat': this.cases.filter(c => c.status === 'active').length,
            'upcoming-events-stat': this.getUpcomingEvents().length
        };
        
        Object.entries(elements).forEach(([id, count]) => {
            const element = this.container.querySelector(`#${id}`);
            if (element) {
                element.textContent = count;
            }
        });
    }

    getRecentCases() {
        return this.cases.slice(0, 5);
    }

    getUpcomingEvents() {
        const now = new Date();
        return this.events
            .filter(event => new Date(event.event_date) > now)
            .slice(0, 5);
    }

    getCaseById(id) {
        return this.cases.find(c => c.id === id);
    }

    getFilteredCases() {
        let filtered = [...this.cases];
        
        // Apply search filter
        if (this.searchQuery) {
            filtered = filtered.filter(c => 
                c.title.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                c.client_name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                c.case_number.toLowerCase().includes(this.searchQuery.toLowerCase())
            );
        }
        
        // Apply status filter
        if (this.filterStatus !== 'all') {
            filtered = filtered.filter(c => c.status === this.filterStatus);
        }
        
        // Apply priority filter
        if (this.filterPriority !== 'all') {
            filtered = filtered.filter(c => c.priority === this.filterPriority);
        }
        
        return filtered;
    }

    updateHeader(title, subtitle) {
        const titleElement = this.container.querySelector('#view-title');
        const subtitleElement = this.container.querySelector('#view-subtitle');
        
        if (titleElement) titleElement.textContent = title;
        if (subtitleElement) subtitleElement.textContent = subtitle;
    }

    updateHeaderActions(view) {
        const actionsContainer = this.container.querySelector('#header-actions');
        
        let actionsHTML = '';
        switch (view) {
            case 'cases':
                actionsHTML = `
                    <button class="btn btn-primary" onclick="lexflowModule.showNewCaseForm()">
                        <svg class="icon" viewBox="0 0 24 24">
                            <path d="M19,*************************************"/>
                        </svg>
                        Nuevo Caso
                    </button>
                `;
                break;
        }
        
        if (actionsContainer) {
            actionsContainer.innerHTML = actionsHTML;
        }
    }

    // Additional methods will be implemented in subsequent parts
    async refreshData() {
        await this.loadData();
        if (this.currentView === 'cases') {
            this.refreshCasesView();
        }
    }

    refreshCasesView() {
        const casesGrid = this.container.querySelector('#cases-grid');
        if (casesGrid) {
            casesGrid.innerHTML = this.getFilteredCases().map(caseItem => this.renderCaseCard(caseItem)).join('');
        }
    }

    saveState() {
        const state = {
            currentView: this.currentView,
            searchQuery: this.searchQuery,
            filterStatus: this.filterStatus,
            filterPriority: this.filterPriority,
            selectedCase: this.selectedCase
        };
        
        localStorage.setItem('lexflow_state', JSON.stringify(state));
    }

    /**
     * Show new case form
     */
    showNewCaseForm() {
        console.log('Module', '[LexFlow] Opening new case form...', );
        
        const modal = document.createElement('div');
        modal.className = 'modal-container lexflow-modal';
        modal.innerHTML = `
            <div class="modal new-case-modal">
                <div class="modal-header">
                    <h3 class="modal-title">Nuevo Caso Legal</h3>
                    <button class="modal-close" id="modal-close">×</button>
                </div>
                <div class="modal-body">
                    <form id="new-case-form" class="form-grid">
                        <div class="form-group">
                            <label for="case-title">Título del Caso *</label>
                            <input type="text" id="case-title" name="title" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="case-type">Tipo de Caso *</label>
                            <select id="case-type" name="case_type" required>
                                <option value="">Seleccionar...</option>
                                <option value="Civil">Civil</option>
                                <option value="Penal">Penal</option>
                                <option value="Laboral">Laboral</option>
                                <option value="Comercial">Comercial</option>
                                <option value="Familia">Familia</option>
                                <option value="Administrativo">Administrativo</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="client-name">Nombre del Cliente *</label>
                            <input type="text" id="client-name" name="client_name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="client-email">Email del Cliente</label>
                            <input type="email" id="client-email" name="client_email">
                        </div>
                        
                        <div class="form-group">
                            <label for="client-phone">Teléfono del Cliente</label>
                            <input type="tel" id="client-phone" name="client_phone">
                        </div>
                        
                        <div class="form-group">
                            <label for="case-priority">Prioridad</label>
                            <select id="case-priority" name="priority">
                                <option value="normal">Normal</option>
                                <option value="low">Baja</option>
                                <option value="high">Alta</option>
                                <option value="urgent">Urgente</option>
                            </select>
                        </div>
                        
                        <div class="form-group form-group-full">
                            <label for="case-description">Descripción</label>
                            <textarea id="case-description" name="description" rows="4"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancel-btn">Cancelar</button>
                    <button type="submit" class="btn btn-primary" id="save-case-btn">Crear Caso</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Event handlers
        modal.querySelector('#modal-close').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.querySelector('#cancel-btn').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.querySelector('#save-case-btn').addEventListener('click', () => {
            this.saveNewCase(modal);
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
        
        // Focus first input
        setTimeout(() => {
            modal.querySelector('#case-title').focus();
        }, 100);
    }

    /**
     * Show new document form
     */
    showNewDocumentForm() {
        console.log('Module', '[LexFlow] Opening new document form...', );
        
        const modal = document.createElement('div');
        modal.className = 'modal-container lexflow-modal';
        modal.innerHTML = `
            <div class="modal new-document-modal">
                <div class="modal-header">
                    <h3 class="modal-title">Subir Documento</h3>
                    <button class="modal-close" id="modal-close">×</button>
                </div>
                <div class="modal-body">
                    <form id="new-document-form" class="form-grid">
                        <div class="form-group">
                            <label for="document-case">Caso Asociado *</label>
                            <select id="document-case" name="case_id" required>
                                <option value="">Seleccionar caso...</option>
                                ${this.cases.map(c => `<option value="${c.id}">${c.title} (#${c.case_number})</option>`).join('')}
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="document-file">Archivo *</label>
                            <div class="file-input-container">
                                <input type="file" id="document-file" name="file" required accept=".pdf,.doc,.docx,.txt,.jpg,.png">
                                <div class="file-input-display">
                                    <span id="file-name">Seleccionar archivo...</span>
                                    <button type="button" class="btn btn-secondary btn-sm">Examinar</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group form-group-full">
                            <label for="document-description">Descripción</label>
                            <textarea id="document-description" name="description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancel-btn">Cancelar</button>
                    <button type="submit" class="btn btn-primary" id="save-document-btn">Subir Documento</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // File input handler
        const fileInput = modal.querySelector('#document-file');
        const fileName = modal.querySelector('#file-name');
        const examineBtn = modal.querySelector('.btn-sm');
        
        examineBtn.addEventListener('click', () => fileInput.click());
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                fileName.textContent = e.target.files[0].name;
            } else {
                fileName.textContent = 'Seleccionar archivo...';
            }
        });
        
        // Event handlers
        modal.querySelector('#modal-close').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.querySelector('#cancel-btn').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.querySelector('#save-document-btn').addEventListener('click', () => {
            this.saveNewDocument(modal);
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    /**
     * Show new event form
     */
    showNewEventForm() {
        console.log('Module', '[LexFlow] Opening new event form...', );
        
        const modal = document.createElement('div');
        modal.className = 'modal-container lexflow-modal';
        modal.innerHTML = `
            <div class="modal new-event-modal">
                <div class="modal-header">
                    <h3 class="modal-title">Agendar Evento</h3>
                    <button class="modal-close" id="modal-close">×</button>
                </div>
                <div class="modal-body">
                    <form id="new-event-form" class="form-grid">
                        <div class="form-group">
                            <label for="event-case">Caso Asociado</label>
                            <select id="event-case" name="case_id">
                                <option value="">Sin caso asociado</option>
                                ${this.cases.map(c => `<option value="${c.id}">${c.title} (#${c.case_number})</option>`).join('')}
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="event-type">Tipo de Evento *</label>
                            <select id="event-type" name="event_type" required>
                                <option value="">Seleccionar...</option>
                                <option value="hearing">Audiencia</option>
                                <option value="meeting">Reunión</option>
                                <option value="deadline">Plazo Legal</option>
                                <option value="appointment">Cita</option>
                                <option value="court">Juzgado</option>
                                <option value="other">Otro</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="event-title">Título del Evento *</label>
                            <input type="text" id="event-title" name="title" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="event-date">Fecha *</label>
                            <input type="date" id="event-date" name="event_date" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="event-time">Hora *</label>
                            <input type="time" id="event-time" name="event_time" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="event-location">Ubicación</label>
                            <input type="text" id="event-location" name="location">
                        </div>
                        
                        <div class="form-group form-group-full">
                            <label for="event-description">Descripción</label>
                            <textarea id="event-description" name="description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancel-btn">Cancelar</button>
                    <button type="submit" class="btn btn-primary" id="save-event-btn">Crear Evento</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Set default date to today
        const today = new Date();
        modal.querySelector('#event-date').value = today.toISOString().split('T')[0];
        modal.querySelector('#event-time').value = '09:00';
        
        // Event handlers
        modal.querySelector('#modal-close').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.querySelector('#cancel-btn').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.querySelector('#save-event-btn').addEventListener('click', () => {
            this.saveNewEvent(modal);
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
        
        // Focus first input
        setTimeout(() => {
            modal.querySelector('#event-title').focus();
        }, 100);
    }

    /**
     * Save new case
     */
    async saveNewCase(modal) {
        const form = modal.querySelector('#new-case-form');
        const formData = new FormData(form);
        
        // Validate required fields
        if (!formData.get('title') || !formData.get('case_type') || !formData.get('client_name')) {
            alert('Por favor complete todos los campos obligatorios.');
            return;
        }
        
        try {
            const caseNumber = `LEG-${new Date().getFullYear()}-${String(this.cases.length + 1).padStart(3, '0')}`;
            const now = new Date().toISOString();
            
            const newCase = {
                case_number: caseNumber,
                title: formData.get('title'),
                case_type: formData.get('case_type'),
                client_name: formData.get('client_name'),
                client_email: formData.get('client_email') || null,
                client_phone: formData.get('client_phone') || null,
                priority: formData.get('priority') || 'normal',
                status: 'active',
                description: formData.get('description') || null,
                created_at: now,
                updated_at: now
            };
            
            if (window.databaseManager && window.databaseManager.isInitialized) {
                const result = await window.databaseManager.run(
                    `INSERT INTO lexflow_cases (case_number, title, case_type, client_name, client_email, client_phone, priority, status, description, created_at, updated_at) 
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [newCase.case_number, newCase.title, newCase.case_type, newCase.client_name, 
                     newCase.client_email, newCase.client_phone, newCase.priority, newCase.status, 
                     newCase.description, newCase.created_at, newCase.updated_at]
                );
                
                newCase.id = result.lastID;
            } else {
                newCase.id = this.cases.length + 1;
            }
            
            this.cases.unshift(newCase);
            this.updateCounts();
            
            if (this.currentView === 'cases') {
                this.refreshCasesView();
            }
            
            document.body.removeChild(modal);
            console.log('Module', '[LexFlow] New case created successfully:', newCase);
            
        } catch (error) {
            console.error('Module', '[LexFlow] Error saving case:', error);
            alert('Error al guardar el caso. Por favor intente nuevamente.');
        }
    }

    /**
     * Save new document
     */
    async saveNewDocument(modal) {
        const form = modal.querySelector('#new-document-form');
        const formData = new FormData(form);
        const fileInput = modal.querySelector('#document-file');
        
        // Validate required fields
        if (!formData.get('case_id') || !fileInput.files.length) {
            alert('Por favor complete todos los campos obligatorios.');
            return;
        }
        
        try {
            const file = fileInput.files[0];
            const now = new Date().toISOString();
            
            const newDocument = {
                case_id: parseInt(formData.get('case_id')),
                filename: file.name,
                file_type: file.type || file.name.split('.').pop(),
                file_size: file.size,
                description: formData.get('description') || null,
                created_at: now
            };
            
            if (window.databaseManager && window.databaseManager.isInitialized) {
                const result = await window.databaseManager.run(
                    `INSERT INTO lexflow_documents (case_id, filename, file_type, file_size, description, created_at) 
                     VALUES (?, ?, ?, ?, ?, ?)`,
                    [newDocument.case_id, newDocument.filename, newDocument.file_type, 
                     newDocument.file_size, newDocument.description, newDocument.created_at]
                );
                
                newDocument.id = result.lastID;
            } else {
                newDocument.id = this.documents.length + 1;
            }
            
            this.documents.unshift(newDocument);
            this.updateCounts();
            
            document.body.removeChild(modal);
            console.log('Module', '[LexFlow] New document uploaded successfully:', newDocument);
            
        } catch (error) {
            console.error('Module', '[LexFlow] Error saving document:', error);
            alert('Error al subir el documento. Por favor intente nuevamente.');
        }
    }

    /**
     * Save new event
     */
    async saveNewEvent(modal) {
        const form = modal.querySelector('#new-event-form');
        const formData = new FormData(form);
        
        // Validate required fields
        if (!formData.get('title') || !formData.get('event_type') || !formData.get('event_date') || !formData.get('event_time')) {
            alert('Por favor complete todos los campos obligatorios.');
            return;
        }
        
        try {
            const eventDateTime = `${formData.get('event_date')}T${formData.get('event_time')}:00Z`;
            const now = new Date().toISOString();
            
            const newEvent = {
                case_id: formData.get('case_id') ? parseInt(formData.get('case_id')) : null,
                event_type: formData.get('event_type'),
                title: formData.get('title'),
                description: formData.get('description') || null,
                event_date: eventDateTime,
                location: formData.get('location') || null,
                status: 'scheduled',
                created_at: now
            };
            
            if (window.databaseManager && window.databaseManager.isInitialized) {
                const result = await window.databaseManager.run(
                    `INSERT INTO lexflow_events (case_id, event_type, title, description, event_date, location, status, created_at) 
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                    [newEvent.case_id, newEvent.event_type, newEvent.title, newEvent.description, 
                     newEvent.event_date, newEvent.location, newEvent.status, newEvent.created_at]
                );
                
                newEvent.id = result.lastID;
            } else {
                newEvent.id = this.events.length + 1;
            }
            
            this.events.push(newEvent);
            this.updateCounts();
            
            document.body.removeChild(modal);
            console.log('Module', '[LexFlow] New event created successfully:', newEvent);
            
        } catch (error) {
            console.error('Module', '[LexFlow] Error saving event:', error);
            alert('Error al crear el evento. Por favor intente nuevamente.');
        }
    }

    openCase(caseId) {
        console.log('Module', '[LexFlow] Opening case:', caseId);
        this.selectedCase = caseId;
        this.showView('case-detail');
    }

    editCase(caseId) {
        console.log('Module', '[LexFlow] Editing case:', caseId);
        const caseToEdit = this.cases.find(c => c.id === caseId);
        if (caseToEdit) {
            this.showEditCaseForm(caseToEdit);
        }
    }

    showCaseDetail(caseId) {
        const caseItem = this.cases.find(c => c.id === caseId);
        if (!caseItem) {
            console.error('Module', '[LexFlow] Case not found:', caseId);
            return;
        }
        
        this.updateHeader(caseItem.title, `Caso #${caseItem.case_number} - ${caseItem.client_name}`);
        
        const caseDocuments = this.documents.filter(doc => doc.case_id === caseId);
        const caseEvents = this.events.filter(event => event.case_id === caseId);
        const caseNotes = this.notes.filter(note => note.case_id === caseId);
        
        const detailHTML = `
            <div class="case-detail-view">
                <div class="case-detail-header">
                    <div class="case-info-grid">
                        <div class="info-card">
                            <h4>Información del Caso</h4>
                            <div class="info-list">
                                <div class="info-item">
                                    <label>Número:</label>
                                    <span>#${caseItem.case_number}</span>
                                </div>
                                <div class="info-item">
                                    <label>Tipo:</label>
                                    <span>${caseItem.case_type}</span>
                                </div>
                                <div class="info-item">
                                    <label>Estado:</label>
                                    <span class="status-badge status-${caseItem.status}">${caseItem.status}</span>
                                </div>
                                <div class="info-item">
                                    <label>Prioridad:</label>
                                    <span class="priority-badge priority-${caseItem.priority}">${caseItem.priority}</span>
                                </div>
                                <div class="info-item">
                                    <label>Creado:</label>
                                    <span>${new Date(caseItem.created_at).toLocaleDateString('es-ES')}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="info-card">
                            <h4>Cliente</h4>
                            <div class="info-list">
                                <div class="info-item">
                                    <label>Nombre:</label>
                                    <span>${caseItem.client_name}</span>
                                </div>
                                ${caseItem.client_email ? `
                                <div class="info-item">
                                    <label>Email:</label>
                                    <span>${caseItem.client_email}</span>
                                </div>
                                ` : ''}
                                ${caseItem.client_phone ? `
                                <div class="info-item">
                                    <label>Teléfono:</label>
                                    <span>${caseItem.client_phone}</span>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                    
                    ${caseItem.description ? `
                    <div class="case-description">
                        <h4>Descripción</h4>
                        <p>${caseItem.description}</p>
                    </div>
                    ` : ''}
                </div>
                
                <div class="case-detail-tabs">
                    <div class="tab-headers">
                        <button class="tab-btn active" data-tab="documents">Documentos (${caseDocuments.length})</button>
                        <button class="tab-btn" data-tab="events">Eventos (${caseEvents.length})</button>
                        <button class="tab-btn" data-tab="notes">Notas (${caseNotes.length})</button>
                    </div>
                    
                    <div class="tab-content">
                        <div class="tab-panel active" id="documents-panel">
                            <div class="documents-list">
                                ${caseDocuments.map(doc => `
                                    <div class="document-item">
                                        <div class="document-icon">📄</div>
                                        <div class="document-info">
                                            <div class="document-name">${doc.filename}</div>
                                            <div class="document-meta">
                                                ${doc.description || 'Sin descripción'} • 
                                                ${this.formatFileSize(doc.file_size)} • 
                                                ${new Date(doc.created_at).toLocaleDateString('es-ES')}
                                            </div>
                                        </div>
                                        <div class="document-actions">
                                            <button class="btn btn-sm">Ver</button>
                                            <button class="btn btn-sm">Descargar</button>
                                        </div>
                                    </div>
                                `).join('')}
                                ${caseDocuments.length === 0 ? '<p class="empty-state">No hay documentos asociados</p>' : ''}
                            </div>
                        </div>
                        
                        <div class="tab-panel" id="events-panel">
                            <div class="events-list">
                                ${caseEvents.map(event => `
                                    <div class="event-item">
                                        <div class="event-date">
                                            <div class="date-day">${new Date(event.event_date).getDate()}</div>
                                            <div class="date-month">${new Date(event.event_date).toLocaleDateString('es-ES', { month: 'short' })}</div>
                                        </div>
                                        <div class="event-info">
                                            <div class="event-title">${event.title}</div>
                                            <div class="event-meta">
                                                ${event.event_type} • 
                                                ${new Date(event.event_date).toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' })}
                                                ${event.location ? ` • ${event.location}` : ''}
                                            </div>
                                            ${event.description ? `<div class="event-description">${event.description}</div>` : ''}
                                        </div>
                                        <div class="event-status status-${event.status}">${event.status}</div>
                                    </div>
                                `).join('')}
                                ${caseEvents.length === 0 ? '<p class="empty-state">No hay eventos programados</p>' : ''}
                            </div>
                        </div>
                        
                        <div class="tab-panel" id="notes-panel">
                            <div class="notes-section">
                                <div class="new-note-form">
                                    <textarea placeholder="Agregar nueva nota..." id="new-note-text"></textarea>
                                    <button class="btn btn-primary btn-sm" onclick="lexflowModule.addNote(${caseId})">Agregar Nota</button>
                                </div>
                                <div class="notes-list">
                                    ${caseNotes.map(note => `
                                        <div class="note-item">
                                            <div class="note-content">${note.content}</div>
                                            <div class="note-meta">${new Date(note.created_at).toLocaleDateString('es-ES')}</div>
                                        </div>
                                    `).join('')}
                                    ${caseNotes.length === 0 ? '<p class="empty-state">No hay notas</p>' : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.mainContent.innerHTML = detailHTML;
        this.updateHeaderActions('case-detail');
        this.setupCaseDetailEventListeners();
    }

    showDocumentsView() {
        this.updateHeader('Documentos', 'Gestión de documentos legales');
        
        const documentsHTML = `
            <div class="documents-view">
                <div class="documents-toolbar">
                    <div class="search-controls">
                        <div class="search-box">
                            <input type="text" placeholder="Buscar documentos..." id="documents-search">
                            <svg class="search-icon" viewBox="0 0 24 24">
                                <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L20.49,19.78L19.78,20.49L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,4A5.5,5.5 0 0,0 4,9.5A5.5,5.5 0 0,0 9.5,15A5.5,5.5 0 0,0 15,9.5A5.5,5.5 0 0,0 9.5,4Z"/>
                            </svg>
                        </div>
                        <select id="case-filter" class="filter-select">
                            <option value="all">Todos los casos</option>
                            ${this.cases.map(c => `<option value="${c.id}">${c.title}</option>`).join('')}
                        </select>
                    </div>
                </div>
                
                <div class="documents-grid">
                    ${this.documents.map(doc => {
                        const caseItem = this.getCaseById(doc.case_id);
                        return `
                            <div class="document-card">
                                <div class="document-header">
                                    <div class="file-type-icon">${this.getFileTypeIcon(doc.file_type)}</div>
                                    <div class="document-actions">
                                        <button class="btn btn-sm">Ver</button>
                                        <button class="btn btn-sm">Descargar</button>
                                    </div>
                                </div>
                                <div class="document-body">
                                    <h4 class="document-name">${doc.filename}</h4>
                                    <div class="document-case">${caseItem ? caseItem.title : 'Sin caso'}</div>
                                    <div class="document-meta">
                                        ${this.formatFileSize(doc.file_size)} • 
                                        ${new Date(doc.created_at).toLocaleDateString('es-ES')}
                                    </div>
                                    ${doc.description ? `<div class="document-description">${doc.description}</div>` : ''}
                                </div>
                            </div>
                        `;
                    }).join('')}
                    ${this.documents.length === 0 ? '<div class="empty-state"><p>No hay documentos</p></div>' : ''}
                </div>
            </div>
        `;
        
        this.mainContent.innerHTML = documentsHTML;
        this.updateHeaderActions('documents');
    }

    showEventsView() {
        this.updateHeader('Eventos y Plazos', 'Calendario de eventos legales');
        
        const eventsHTML = `
            <div class="events-view">
                <div class="events-toolbar">
                    <div class="search-controls">
                        <div class="search-box">
                            <input type="text" placeholder="Buscar eventos..." id="events-search">
                            <svg class="search-icon" viewBox="0 0 24 24">
                                <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L20.49,19.78L19.78,20.49L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,4A5.5,5.5 0 0,0 4,9.5A5.5,5.5 0 0,0 9.5,15A5.5,5.5 0 0,0 15,9.5A5.5,5.5 0 0,0 9.5,4Z"/>
                            </svg>
                        </div>
                        <select id="event-type-filter" class="filter-select">
                            <option value="all">Todos los tipos</option>
                            <option value="hearing">Audiencias</option>
                            <option value="meeting">Reuniones</option>
                            <option value="deadline">Plazos Legales</option>
                            <option value="appointment">Citas</option>
                            <option value="court">Juzgado</option>
                        </select>
                    </div>
                </div>
                
                <div class="events-timeline">
                    ${this.events.sort((a, b) => new Date(a.event_date) - new Date(b.event_date)).map(event => {
                        const caseItem = this.getCaseById(event.case_id);
                        const eventDate = new Date(event.event_date);
                        const isUpcoming = eventDate > new Date();
                        
                        return `
                            <div class="event-timeline-item ${isUpcoming ? 'upcoming' : 'past'}">
                                <div class="event-date-marker">
                                    <div class="date-day">${eventDate.getDate()}</div>
                                    <div class="date-month">${eventDate.toLocaleDateString('es-ES', { month: 'short' })}</div>
                                    <div class="date-year">${eventDate.getFullYear()}</div>
                                </div>
                                <div class="event-content">
                                    <div class="event-header">
                                        <h4 class="event-title">${event.title}</h4>
                                        <span class="event-type-badge">${event.event_type}</span>
                                    </div>
                                    <div class="event-details">
                                        <div class="event-time">
                                            📅 ${eventDate.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' })}
                                        </div>
                                        ${event.location ? `<div class="event-location">📍 ${event.location}</div>` : ''}
                                        ${caseItem ? `<div class="event-case">📋 ${caseItem.title}</div>` : ''}
                                    </div>
                                    ${event.description ? `<div class="event-description">${event.description}</div>` : ''}
                                    <div class="event-status status-${event.status}">${event.status}</div>
                                </div>
                            </div>
                        `;
                    }).join('')}
                    ${this.events.length === 0 ? '<div class="empty-state"><p>No hay eventos programados</p></div>' : ''}
                </div>
            </div>
        `;
        
        this.mainContent.innerHTML = eventsHTML;
        this.updateHeaderActions('events');
    }

    toggleCasesView(viewType) {
        const casesGrid = this.container.querySelector('#cases-grid');
        if (casesGrid) {
            casesGrid.className = viewType === 'list' ? 'cases-list' : 'cases-grid';
        }
    }

    focusSearch() {
        const searchInput = this.container.querySelector('#cases-search');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    /**
     * Setup case detail event listeners
     */
    setupCaseDetailEventListeners() {
        const tabButtons = this.container.querySelectorAll('.tab-btn');
        const tabPanels = this.container.querySelectorAll('.tab-panel');
        
        tabButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                // Remove active from all tabs
                tabButtons.forEach(b => b.classList.remove('active'));
                tabPanels.forEach(p => p.classList.remove('active'));
                
                // Add active to clicked tab
                btn.classList.add('active');
                const targetPanel = this.container.querySelector(`#${btn.dataset.tab}-panel`);
                if (targetPanel) {
                    targetPanel.classList.add('active');
                }
            });
        });
    }

    /**
     * Add note to case
     */
    async addNote(caseId) {
        const textarea = this.container.querySelector('#new-note-text');
        const content = textarea.value.trim();
        
        if (!content) {
            alert('Por favor escriba una nota.');
            return;
        }
        
        try {
            const now = new Date().toISOString();
            const newNote = {
                case_id: caseId,
                content: content,
                created_at: now
            };
            
            if (window.databaseManager && window.databaseManager.isInitialized) {
                const result = await window.databaseManager.run(
                    `INSERT INTO lexflow_notes (case_id, content, created_at) VALUES (?, ?, ?)`,
                    [newNote.case_id, newNote.content, newNote.created_at]
                );
                newNote.id = result.lastID;
            } else {
                newNote.id = this.notes.length + 1;
            }
            
            this.notes.unshift(newNote);
            textarea.value = '';
            
            // Refresh the notes panel
            this.showCaseDetail(caseId);
            
        } catch (error) {
            console.error('Module', '[LexFlow] Error adding note:', error);
            alert('Error al agregar la nota.');
        }
    }

    /**
     * Format file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Get file type icon
     */
    getFileTypeIcon(fileType) {
        const type = fileType.toLowerCase();
        if (type.includes('pdf')) return '📄';
        if (type.includes('word') || type.includes('doc')) return '📝';
        if (type.includes('excel') || type.includes('sheet')) return '📊';
        if (type.includes('image') || type.includes('jpg') || type.includes('png')) return '🖼️';
        if (type.includes('text') || type.includes('txt')) return '📃';
        return '📎';
    }

    /**
     * Show edit case form
     */
    showEditCaseForm(caseItem) {
        const modal = document.createElement('div');
        modal.className = 'modal-container lexflow-modal';
        modal.innerHTML = `
            <div class="modal edit-case-modal">
                <div class="modal-header">
                    <h3 class="modal-title">Editar Caso: ${caseItem.title}</h3>
                    <button class="modal-close" id="modal-close">×</button>
                </div>
                <div class="modal-body">
                    <form id="edit-case-form" class="form-grid">
                        <div class="form-group">
                            <label for="edit-case-title">Título del Caso *</label>
                            <input type="text" id="edit-case-title" name="title" value="${caseItem.title}" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="edit-case-type">Tipo de Caso *</label>
                            <select id="edit-case-type" name="case_type" required>
                                <option value="Civil" ${caseItem.case_type === 'Civil' ? 'selected' : ''}>Civil</option>
                                <option value="Penal" ${caseItem.case_type === 'Penal' ? 'selected' : ''}>Penal</option>
                                <option value="Laboral" ${caseItem.case_type === 'Laboral' ? 'selected' : ''}>Laboral</option>
                                <option value="Comercial" ${caseItem.case_type === 'Comercial' ? 'selected' : ''}>Comercial</option>
                                <option value="Familia" ${caseItem.case_type === 'Familia' ? 'selected' : ''}>Familia</option>
                                <option value="Administrativo" ${caseItem.case_type === 'Administrativo' ? 'selected' : ''}>Administrativo</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="edit-client-name">Nombre del Cliente *</label>
                            <input type="text" id="edit-client-name" name="client_name" value="${caseItem.client_name}" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="edit-client-email">Email del Cliente</label>
                            <input type="email" id="edit-client-email" name="client_email" value="${caseItem.client_email || ''}">
                        </div>
                        
                        <div class="form-group">
                            <label for="edit-client-phone">Teléfono del Cliente</label>
                            <input type="tel" id="edit-client-phone" name="client_phone" value="${caseItem.client_phone || ''}">
                        </div>
                        
                        <div class="form-group">
                            <label for="edit-case-priority">Prioridad</label>
                            <select id="edit-case-priority" name="priority">
                                <option value="low" ${caseItem.priority === 'low' ? 'selected' : ''}>Baja</option>
                                <option value="normal" ${caseItem.priority === 'normal' ? 'selected' : ''}>Normal</option>
                                <option value="high" ${caseItem.priority === 'high' ? 'selected' : ''}>Alta</option>
                                <option value="urgent" ${caseItem.priority === 'urgent' ? 'selected' : ''}>Urgente</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="edit-case-status">Estado</label>
                            <select id="edit-case-status" name="status">
                                <option value="active" ${caseItem.status === 'active' ? 'selected' : ''}>Activo</option>
                                <option value="closed" ${caseItem.status === 'closed' ? 'selected' : ''}>Cerrado</option>
                                <option value="suspended" ${caseItem.status === 'suspended' ? 'selected' : ''}>Suspendido</option>
                            </select>
                        </div>
                        
                        <div class="form-group form-group-full">
                            <label for="edit-case-description">Descripción</label>
                            <textarea id="edit-case-description" name="description" rows="4">${caseItem.description || ''}</textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancel-btn">Cancelar</button>
                    <button type="submit" class="btn btn-primary" id="update-case-btn">Actualizar Caso</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Event handlers
        modal.querySelector('#modal-close').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.querySelector('#cancel-btn').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.querySelector('#update-case-btn').addEventListener('click', () => {
            this.updateCase(caseItem.id, modal);
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    /**
     * Update case
     */
    async updateCase(caseId, modal) {
        const form = modal.querySelector('#edit-case-form');
        const formData = new FormData(form);
        
        try {
            const updatedCase = {
                title: formData.get('title'),
                case_type: formData.get('case_type'),
                client_name: formData.get('client_name'),
                client_email: formData.get('client_email') || null,
                client_phone: formData.get('client_phone') || null,
                priority: formData.get('priority'),
                status: formData.get('status'),
                description: formData.get('description') || null,
                updated_at: new Date().toISOString()
            };
            
            if (window.databaseManager && window.databaseManager.isInitialized) {
                await window.databaseManager.run(
                    `UPDATE lexflow_cases SET title = ?, case_type = ?, client_name = ?, client_email = ?, 
                     client_phone = ?, priority = ?, status = ?, description = ?, updated_at = ? WHERE id = ?`,
                    [updatedCase.title, updatedCase.case_type, updatedCase.client_name, 
                     updatedCase.client_email, updatedCase.client_phone, updatedCase.priority, 
                     updatedCase.status, updatedCase.description, updatedCase.updated_at, caseId]
                );
            }
            
            // Update in memory
            const caseIndex = this.cases.findIndex(c => c.id === caseId);
            if (caseIndex !== -1) {
                this.cases[caseIndex] = { ...this.cases[caseIndex], ...updatedCase };
            }
            
            this.updateCounts();
            
            if (this.currentView === 'cases') {
                this.refreshCasesView();
            } else if (this.currentView === 'case-detail' && this.selectedCase === caseId) {
                this.showCaseDetail(caseId);
            }
            
            document.body.removeChild(modal);
            console.log('Module', '[LexFlow] Case updated successfully');
            
        } catch (error) {
            console.error('Module', '[LexFlow] Error updating case:', error);
            alert('Error al actualizar el caso.');
        }
    }
}

// Create global instance
window.lexflowModule = new LexFlowModule();

console.log('Module', '[LexFlow] Module class defined and global instance created successfully', );