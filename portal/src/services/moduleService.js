const path = require('path');
const fs = require('fs').promises;
const tar = require('tar');
const config = require('../config');
const logger = require('../middleware/logger');

class ModuleService {
  constructor() {
    this.modulesPath = path.join(__dirname, '../../downloads/modules');
    this.downloadStats = new Map();
    
    // Module definitions from config
    this.availableModules = config.modules || {};
  }

  // Get all available modules with their information
  async getAllModules() {
    try {
      const modules = [];
      
      for (const [moduleId, moduleConfig] of Object.entries(this.availableModules)) {
        const moduleInfo = await this.getModuleInfo(moduleId);
        modules.push({
          id: moduleId,
          ...moduleConfig,
          ...moduleInfo,
          downloadUrl: `/modules/${moduleId}/download`,
          infoUrl: `/modules/${moduleId}`
        });
      }
      
      return modules;
    } catch (error) {
      logger.error('Error al obtener módulos:', error);
      return [];
    }
  }

  // Get specific module information
  async getModuleInfo(moduleId) {
    try {
      if (!this.availableModules[moduleId]) {
        return null;
      }

      // Check if module has downloadable versions
      const modulePath = path.join(this.modulesPath, moduleId);
      const versions = await this.getModuleVersions(moduleId);
      const latestVersion = versions.length > 0 ? versions[0] : null;
      
      let manifest = null;
      let downloadable = false;
      let packageSize = 0;

      if (latestVersion) {
        const manifestPath = path.join(modulePath, latestVersion, 'manifest.json');
        try {
          const manifestContent = await fs.readFile(manifestPath, 'utf8');
          manifest = JSON.parse(manifestContent);
          downloadable = true;
          
          // Get package size
          const packagePath = path.join(modulePath, latestVersion, `${moduleId}-${latestVersion}.tar.gz`);
          try {
            const stats = await fs.stat(packagePath);
            packageSize = stats.size;
          } catch (e) {
            // Package file doesn't exist
          }
        } catch (e) {
          logger.warn(`No manifest found for ${moduleId} v${latestVersion}`);
        }
      }

      return {
        versions,
        latestVersion,
        downloadable,
        packageSize: this.formatBytes(packageSize),
        downloads: this.getDownloadCount(moduleId),
        manifest: manifest || {},
        status: downloadable ? 'available' : 'coming_soon'
      };
    } catch (error) {
      logger.error(`Error al obtener info del módulo ${moduleId}:`, error);
      return null;
    }
  }

  // Get available versions for a module
  async getModuleVersions(moduleId) {
    try {
      const modulePath = path.join(this.modulesPath, moduleId);
      const entries = await fs.readdir(modulePath, { withFileTypes: true });
      
      const versionDirs = entries
        .filter(entry => entry.isDirectory() && /^\d+\.\d+\.\d+$/.test(entry.name))
        .map(entry => entry.name);
      
      // Filter to only include versions that have valid manifests and packages
      const validVersions = [];
      for (const version of versionDirs) {
        const manifestPath = path.join(modulePath, version, 'manifest.json');
        const packagePath = path.join(modulePath, version, `${moduleId}-${version}.tar.gz`);
        
        try {
          // Check if both manifest and package exist
          await fs.access(manifestPath);
          await fs.access(packagePath);
          validVersions.push(version);
        } catch (e) {
          // Skip versions without manifest or package
          continue;
        }
      }
      
      // Sort valid versions in descending order (latest first)
      validVersions.sort((a, b) => {
        const versionA = a.substring(1).split('.').map(n => parseInt(n));
        const versionB = b.substring(1).split('.').map(n => parseInt(n));
        
        for (let i = 0; i < Math.max(versionA.length, versionB.length); i++) {
          const numA = versionA[i] || 0;
          const numB = versionB[i] || 0;
          if (numA !== numB) {
            return numB - numA;
          }
        }
        return 0;
      });

      return validVersions;
    } catch (error) {
      logger.error(`Error al obtener versiones de ${moduleId}:`, error);
      return [];
    }
  }

  // Get download URL for a specific module version
  async getDownloadUrl(moduleId, version = null) {
    try {
      const moduleInfo = await this.getModuleInfo(moduleId);
      if (!moduleInfo || !moduleInfo.downloadable) {
        return null;
      }

      const targetVersion = version || moduleInfo.latestVersion;
      if (!targetVersion) {
        return null;
      }

      const packagePath = path.join(this.modulesPath, moduleId, targetVersion, `${moduleId}-${targetVersion}.tar.gz`);
      
      // Verify package exists
      try {
        await fs.access(packagePath);
        return {
          moduleId,
          version: targetVersion,
          filename: `${moduleId}-${targetVersion}.tar.gz`,
          path: packagePath,
          type: 'local'
        };
      } catch {
        return null;
      }
    } catch (error) {
      logger.error(`Error al obtener URL de descarga ${moduleId}:`, error);
      return null;
    }
  }

  // Log module download
  async logDownload(downloadInfo) {
    try {
      const logEntry = {
        ...downloadInfo,
        id: Date.now().toString(),
        timestamp: new Date(),
        ip: downloadInfo.ip || 'unknown',
        userAgent: downloadInfo.userAgent || 'unknown'
      };
      
      // Log to file
      logger.info('Descarga de módulo registrada:', logEntry);
      
      // Update stats in memory
      const key = `${downloadInfo.moduleId}_${downloadInfo.version}`;
      if (!this.downloadStats.has(key)) {
        this.downloadStats.set(key, 0);
      }
      this.downloadStats.set(key, this.downloadStats.get(key) + 1);
      
      return logEntry;
    } catch (error) {
      logger.error('Error al registrar descarga de módulo:', error);
      throw error;
    }
  }

  // Get download statistics
  getDownloadStats() {
    try {
      const stats = {
        totalDownloads: Array.from(this.downloadStats.values()).reduce((a, b) => a + b, 0),
        downloadsByModule: {},
        topModules: []
      };

      // Calculate downloads by module
      for (const [key, count] of this.downloadStats.entries()) {
        const [moduleId] = key.split('_');
        if (!stats.downloadsByModule[moduleId]) {
          stats.downloadsByModule[moduleId] = 0;
        }
        stats.downloadsByModule[moduleId] += count;
      }

      // Get top modules
      stats.topModules = Object.entries(stats.downloadsByModule)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([moduleId, downloads]) => ({
          moduleId,
          moduleName: this.availableModules[moduleId]?.name || moduleId,
          downloads
        }));

      return stats;
    } catch (error) {
      logger.error('Error al obtener estadísticas de descargas:', error);
      return {
        totalDownloads: 0,
        downloadsByModule: {},
        topModules: []
      };
    }
  }

  // Get download count for a specific module
  getDownloadCount(moduleId) {
    let totalDownloads = 0;
    for (const [key, count] of this.downloadStats.entries()) {
      if (key.startsWith(`${moduleId}_`)) {
        totalDownloads += count;
      }
    }
    return totalDownloads;
  }

  // Verify module compatibility
  async verifyCompatibility(moduleId, version = null) {
    try {
      const moduleInfo = await this.getModuleInfo(moduleId);
      if (!moduleInfo || !moduleInfo.manifest) {
        return { compatible: false, reason: 'Module not found or no manifest' };
      }

      const manifest = moduleInfo.manifest;
      const compatibility = manifest.compatibility || {};

      // Check CoreDesk version compatibility
      if (compatibility.coredesk) {
        const requiredVersion = compatibility.coredesk.replace('>=', '');
        const currentVersion = config.appVersion;
        
        if (!this.compareVersions(currentVersion, requiredVersion)) {
          return {
            compatible: false,
            reason: `Requires CoreDesk ${compatibility.coredesk}, current version: ${currentVersion}`
          };
        }
      }

      return { compatible: true };
    } catch (error) {
      logger.error(`Error al verificar compatibilidad ${moduleId}:`, error);
      return { compatible: false, reason: 'Error checking compatibility' };
    }
  }

  // Compare version strings (simple implementation)
  compareVersions(current, required) {
    const currentParts = current.split('.').map(n => parseInt(n));
    const requiredParts = required.split('.').map(n => parseInt(n));
    
    for (let i = 0; i < Math.max(currentParts.length, requiredParts.length); i++) {
      const currentNum = currentParts[i] || 0;
      const requiredNum = requiredParts[i] || 0;
      
      if (currentNum > requiredNum) return true;
      if (currentNum < requiredNum) return false;
    }
    return true; // Equal versions
  }

  // Format bytes to human readable format
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Get module categories
  getModuleCategories() {
    const categories = new Set();
    
    for (const moduleConfig of Object.values(this.availableModules)) {
      if (moduleConfig.category) {
        categories.add(moduleConfig.category);
      }
    }
    
    return Array.from(categories);
  }

  // Search modules
  async searchModules(query, category = null) {
    try {
      const allModules = await this.getAllModules();
      
      return allModules.filter(module => {
        const matchesQuery = !query || 
          module.name.toLowerCase().includes(query.toLowerCase()) ||
          module.description.toLowerCase().includes(query.toLowerCase()) ||
          (module.features && module.features.some(feature => 
            feature.toLowerCase().includes(query.toLowerCase())
          ));
          
        const matchesCategory = !category || module.category === category;
        
        return matchesQuery && matchesCategory;
      });
    } catch (error) {
      logger.error('Error al buscar módulos:', error);
      return [];
    }
  }

  // Get module package for CoreDesk framework
  async getModulePackage(moduleId, version = null) {
    try {
      logger.info(`Getting module package for ${moduleId} v${version || 'latest'}`);
      
      // Get module info
      const moduleInfo = await this.getModuleInfo(moduleId);
      if (!moduleInfo || !moduleInfo.downloadable) {
        return null;
      }

      // Use specified version or latest
      const targetVersion = version || moduleInfo.latestVersion;
      if (!targetVersion) {
        return null;
      }

      // Paths
      const versionPath = path.join(this.modulesPath, moduleId, targetVersion);
      const manifestPath = path.join(versionPath, 'manifest.json');
      const packagePath = path.join(versionPath, `${moduleId}-${targetVersion}.tar.gz`);

      // Read manifest
      let manifest;
      try {
        const manifestContent = await fs.readFile(manifestPath, 'utf8');
        manifest = JSON.parse(manifestContent);
      } catch (error) {
        logger.error(`Failed to read manifest for ${moduleId} v${targetVersion}:`, error);
        return null;
      }

      // Check if we have pre-extracted files (for faster response)
      const extractedPath = path.join(versionPath, 'extracted');
      let moduleCode = '';
      let styles = '';
      let assets = {};

      try {
        // Try to read pre-extracted files first
        const codePath = path.join(extractedPath, `${moduleId}.js`);
        const stylesPath = path.join(extractedPath, `${moduleId}.css`);
        
        try {
          moduleCode = await fs.readFile(codePath, 'utf8');
        } catch (e) {
          // Try alternative paths
          const altCodePath = path.join(extractedPath, 'module.js');
          try {
            moduleCode = await fs.readFile(altCodePath, 'utf8');
          } catch (e2) {
            logger.warn(`No module code found for ${moduleId} v${targetVersion}`);
          }
        }

        try {
          styles = await fs.readFile(stylesPath, 'utf8');
        } catch (e) {
          // Try alternative paths
          const altStylesPath = path.join(extractedPath, 'styles.css');
          try {
            styles = await fs.readFile(altStylesPath, 'utf8');
          } catch (e2) {
            logger.info(`No styles found for ${moduleId} v${targetVersion}`);
          }
        }

        // Read assets directory if exists
        const assetsPath = path.join(extractedPath, 'assets');
        try {
          const assetFiles = await fs.readdir(assetsPath);
          for (const file of assetFiles) {
            const filePath = path.join(assetsPath, file);
            const stat = await fs.stat(filePath);
            if (stat.isFile()) {
              // For now, just track asset metadata
              assets[file] = {
                size: stat.size,
                path: `modules/${moduleId}/${targetVersion}/assets/${file}`
              };
            }
          }
        } catch (e) {
          // No assets directory
        }

      } catch (error) {
        logger.warn(`Pre-extracted files not found for ${moduleId} v${targetVersion}, will extract from package`);
        
        // Extract from tar.gz if pre-extracted files don't exist
        try {
          await this.extractModulePackage(packagePath, extractedPath);
          
          // Try reading again after extraction
          const codePath = path.join(extractedPath, `${moduleId}.js`);
          const stylesPath = path.join(extractedPath, `${moduleId}.css`);
          
          try {
            moduleCode = await fs.readFile(codePath, 'utf8');
          } catch (e) {
            const altCodePath = path.join(extractedPath, 'module.js');
            try {
              moduleCode = await fs.readFile(altCodePath, 'utf8');
            } catch (e2) {
              logger.error(`Module code not found after extraction for ${moduleId}`);
            }
          }

          try {
            styles = await fs.readFile(stylesPath, 'utf8');
          } catch (e) {
            logger.info(`No styles found after extraction for ${moduleId}`);
          }

        } catch (extractError) {
          logger.error(`Failed to extract module package ${moduleId}:`, extractError);
        }
      }

      // For built-in modules, generate proper class definitions instead of window references
      if (!moduleCode || moduleCode.trim() === '') {
        moduleCode = this.generateModuleClassCode(moduleId, targetVersion);
        
        // Set default styles if not found
        if (!styles) {
          styles = this.generateDefaultStyles(moduleId);
        }
      }

      // Ensure manifest has required fields
      if (!manifest.main) {
        manifest.main = moduleId.charAt(0).toUpperCase() + moduleId.slice(1) + 'Module';
      }
      if (!manifest.id) {
        manifest.id = moduleId;
      }
      if (!manifest.name) {
        manifest.name = moduleId.charAt(0).toUpperCase() + moduleId.slice(1);
      }
      if (!manifest.version) {
        manifest.version = targetVersion;
      }

      // Clean up line endings in module code to prevent JavaScript execution issues
      const cleanModuleCode = moduleCode.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
      
      // Return package in CoreDesk expected format
      return {
        manifest: manifest,
        code: cleanModuleCode,
        moduleCode: cleanModuleCode, // Alias for compatibility
        styles: styles,
        assets: assets,
        downloadUrl: `/modules/${moduleId}/${targetVersion}/${moduleId}-${targetVersion}.tar.gz`,
        checksum: manifest.checksum || null,
        size: manifest.size || 0
      };

    } catch (error) {
      logger.error(`Error getting module package ${moduleId}:`, error);
      return null;
    }
  }

  // Extract module package from tar.gz
  async extractModulePackage(packagePath, targetPath) {
    try {
      // Create target directory
      await fs.mkdir(targetPath, { recursive: true });

      // Extract tar.gz
      await tar.x({
        file: packagePath,
        cwd: targetPath,
        strip: 1 // Strip the first directory level
      });

      logger.info(`Extracted module package to ${targetPath}`);
    } catch (error) {
      logger.error(`Failed to extract module package:`, error);
      throw error;
    }
  }

  // Generate proper module class code for built-in modules
  generateModuleClassCode(moduleId, version) {
    const className = moduleId.charAt(0).toUpperCase() + moduleId.slice(1) + 'Module';
    const moduleVersion = version || '1.0.0';
    
    // Build the code using simple string concatenation to avoid template literal issues
    let code = 'class ' + className + ' {\n';
    code += '  constructor(packageData) {\n';
    code += '    this.packageData = packageData;\n';
    code += '    this.name = \'' + moduleId + '\';\n';
    code += '    this.version = \'' + moduleVersion + '\';\n';
    code += '    this.isLoaded = false;\n';
    code += '    this.isActive = false;\n';
    code += '    this.moduleId = \'' + moduleId + '\';\n';
    code += '    \n';
    code += '    console.log(\'' + className + ' constructor initialized\');\n';
    code += '  }\n\n';
    
    code += '  async initialize() {\n';
    code += '    console.log(\'' + className + ' initializing...\');\n';
    code += '    this.isLoaded = true;\n';
    code += '    return true;\n';
    code += '  }\n\n';
    
    code += '  async activate() {\n';
    code += '    console.log(\'' + className + ' activating...\');\n';
    code += '    this.isActive = true;\n';
    code += '    return true;\n';
    code += '  }\n\n';
    
    code += '  async deactivate() {\n';
    code += '    console.log(\'' + className + ' deactivating...\');\n';
    code += '    this.isActive = false;\n';
    code += '    return true;\n';
    code += '  }\n\n';
    
    code += '  render() {\n';
    code += '    const moduleContent = document.createElement(\'div\');\n';
    code += '    moduleContent.className = \'' + moduleId + '-module\';\n';
    code += '    \n';
    code += '    moduleContent.innerHTML = \'\' +\n';
    code += '      \'<div class="' + moduleId + '-header">\' +\n';
    code += '        \'<h2>\' + (this.name ? this.name.charAt(0).toUpperCase() + this.name.slice(1) : \'' + moduleId + '\') + \' Module</h2>\' +\n';
    code += '        \'<p>Version: \' + this.version + \'</p>\' +\n';
    code += '        \'<p>Status: \' + (this.isActive ? \'Active\' : \'Inactive\') + \'</p>\' +\n';
    code += '      \'</div>\' +\n';
    code += '      \'<div class="' + moduleId + '-body">\' +\n';
    code += '        \'<p>🚀 Module \' + this.name + \' is now active and running!</p>\' +\n';
    code += '        \'<div class="' + moduleId + '-features">\' +\n';
    code += '          \'<h3>Features:</h3>\' +\n';
    code += '          \'<ul>\' +\n';
    code += '            \'<li>✅ Successfully installed</li>\' +\n';
    code += '            \'<li>✅ Ready for use</li>\' +\n';
    code += '            \'<li>✅ Dynamic loading capability</li>\' +\n';
    code += '            \'<li>✅ Persistent storage</li>\' +\n';
    code += '          \'</ul>\' +\n';
    code += '        \'</div>\' +\n';
    code += '      \'</div>\';\n';
    code += '    \n';
    code += '    return moduleContent;\n';
    code += '  }\n\n';
    
    code += '  getInfo() {\n';
    code += '    return {\n';
    code += '      name: this.name,\n';
    code += '      version: this.version,\n';
    code += '      moduleId: this.moduleId,\n';
    code += '      isLoaded: this.isLoaded,\n';
    code += '      isActive: this.isActive,\n';
    code += '      type: \'built-in\'\n';
    code += '    };\n';
    code += '  }\n';
    code += '}\n\n';
    
    code += '// Export the class\n';
    code += className + ';';
    
    return code;
  }

  // Generate default styles for built-in modules
  generateDefaultStyles(moduleId) {
    const styles = {
      lexflow: `
        /* LexFlow Module Styles */
        .lexflow-module { display: flex; height: 100%; width: 100%; flex-direction: column; }
        .lexflow-header { padding: 20px; background: var(--header-bg, #f5f5f5); border-bottom: 1px solid var(--border-color, #ddd); }
        .lexflow-header h2 { margin: 0 0 10px 0; color: var(--text-color, #333); }
        .lexflow-header p { margin: 5px 0; color: var(--text-secondary, #666); }
        .lexflow-body { padding: 20px; flex: 1; }
        .lexflow-features { margin-top: 20px; }
        .lexflow-features h3 { color: var(--text-color, #333); }
        .lexflow-features ul { list-style: none; padding: 0; }
        .lexflow-features li { padding: 5px 0; }
        .lexflow-layout { display: flex; width: 100%; height: 100%; }
        .lexflow-sidebar { width: 260px; background: var(--sidebar-bg, #f8f9fa); border-right: 1px solid var(--border-color, #ddd); }
        .lexflow-main { flex: 1; display: flex; flex-direction: column; overflow: hidden; }
      `,
      protocolx: `
        /* ProtocolX Module Styles */
        .protocolx-module { display: flex; height: 100%; width: 100%; flex-direction: column; }
        .protocolx-header { padding: 20px; background: var(--header-bg, #f5f5f5); border-bottom: 1px solid var(--border-color, #ddd); }
        .protocolx-header h2 { margin: 0 0 10px 0; color: var(--text-color, #333); }
        .protocolx-header p { margin: 5px 0; color: var(--text-secondary, #666); }
        .protocolx-body { padding: 20px; flex: 1; }
        .protocolx-features { margin-top: 20px; }
        .protocolx-features h3 { color: var(--text-color, #333); }
        .protocolx-features ul { list-style: none; padding: 0; }
        .protocolx-features li { padding: 5px 0; }
        .protocolx-layout { display: flex; width: 100%; height: 100%; }
        .protocolx-sidebar { width: 280px; background: var(--sidebar-bg, #f8f9fa); border-right: 1px solid var(--border-color, #ddd); }
        .protocolx-main { flex: 1; display: flex; flex-direction: column; overflow: hidden; }
      `,
      auditpro: `
        /* AuditPro Module Styles */
        .auditpro-module { display: flex; height: 100%; width: 100%; flex-direction: column; }
        .auditpro-header { padding: 20px; background: var(--header-bg, #f5f5f5); border-bottom: 1px solid var(--border-color, #ddd); }
        .auditpro-header h2 { margin: 0 0 10px 0; color: var(--text-color, #333); }
        .auditpro-header p { margin: 5px 0; color: var(--text-secondary, #666); }
        .auditpro-body { padding: 20px; flex: 1; }
        .auditpro-features { margin-top: 20px; }
        .auditpro-features h3 { color: var(--text-color, #333); }
        .auditpro-features ul { list-style: none; padding: 0; }
        .auditpro-features li { padding: 5px 0; }
      `,
      finsync: `
        /* FinSync Module Styles */
        .finsync-module { display: flex; height: 100%; width: 100%; flex-direction: column; }
        .finsync-header { padding: 20px; background: var(--header-bg, #f5f5f5); border-bottom: 1px solid var(--border-color, #ddd); }
        .finsync-header h2 { margin: 0 0 10px 0; color: var(--text-color, #333); }
        .finsync-header p { margin: 5px 0; color: var(--text-secondary, #666); }
        .finsync-body { padding: 20px; flex: 1; }
        .finsync-features { margin-top: 20px; }
        .finsync-features h3 { color: var(--text-color, #333); }
        .finsync-features ul { list-style: none; padding: 0; }
        .finsync-features li { padding: 5px 0; }
      `
    };

    return styles[moduleId] || `
      /* Default Module Styles */
      .${moduleId}-module { display: flex; height: 100%; width: 100%; flex-direction: column; }
      .${moduleId}-header { padding: 20px; background: var(--header-bg, #f5f5f5); border-bottom: 1px solid var(--border-color, #ddd); }
      .${moduleId}-header h2 { margin: 0 0 10px 0; color: var(--text-color, #333); }
      .${moduleId}-header p { margin: 5px 0; color: var(--text-secondary, #666); }
      .${moduleId}-body { padding: 20px; flex: 1; }
    `;
  }
}

module.exports = new ModuleService();