const express = require('express');
const semver = require('semver');
const config = require('../config');
const logger = require('../middleware/logger');
const { asyncHandler } = require('../middleware/errorHandler');
const updateService = require('../services/updateService');
const downloadService = require('../services/downloadService');
const moduleService = require('../services/moduleService');

const router = express.Router();

// Middleware para validar API key (si es necesario)
const validateApiKey = (req, res, next) => {
  // Para endpoints públicos, no requerir API key
  const publicEndpoints = ['/version/latest', '/version/check', '/releases', '/modules', '/config'];
  
  console.log(`API Request Path: ${req.path}`); // Debug log
  if (publicEndpoints.some(endpoint => req.path.startsWith(endpoint))) {
    return next();
  }
  
  const apiKey = req.headers['x-api-key'] || req.query.api_key;
  
  // TODO: Implementar validación de API key real
  if (!apiKey) {
    return res.status(401).json({
      success: false,
      message: 'API key requerida'
    });
  }
  
  next();
};

router.use(validateApiKey);

// Obtener la última versión disponible
router.get('/version/latest', asyncHandler(async (req, res) => {
  try {
    const latestVersion = await updateService.getLatestVersion();
    const releaseInfo = await updateService.getReleaseInfo(latestVersion);
    
    res.json({
      success: true,
      version: latestVersion,
      release: releaseInfo,
      downloadUrls: await downloadService.getDownloadUrls(latestVersion)
    });
  } catch (error) {
    logger.error('Error al obtener última versión:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener información de versión'
    });
  }
}));

// Verificar si hay actualizaciones disponibles
router.get('/version/check/:current?', asyncHandler(async (req, res) => {
  const currentVersion = req.params.current || req.query.version;
  
  if (!currentVersion) {
    return res.status(400).json({
      success: false,
      message: 'Versión actual es requerida'
    });
  }
  
  if (!semver.valid(currentVersion)) {
    return res.status(400).json({
      success: false,
      message: 'Formato de versión inválido'
    });
  }
  
  try {
    const latestVersion = await updateService.getLatestVersion();
    const hasUpdate = semver.gt(latestVersion, currentVersion);
    
    const response = {
      success: true,
      currentVersion,
      latestVersion,
      hasUpdate,
      updateRequired: false // TODO: Implementar lógica de actualizaciones obligatorias
    };
    
    if (hasUpdate) {
      const releaseInfo = await updateService.getReleaseInfo(latestVersion);
      response.releaseNotes = releaseInfo?.notes;
      response.downloadUrls = await downloadService.getDownloadUrls(latestVersion);
      response.releaseDate = releaseInfo?.publishedAt;
    }
    
    res.json(response);
  } catch (error) {
    logger.error(`Error al verificar actualizaciones para v${currentVersion}:`, error);
    res.status(500).json({
      success: false,
      message: 'Error al verificar actualizaciones'
    });
  }
}));

// Obtener lista de todas las versiones
router.get('/releases', asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    channel = 'stable',
    includePrerelease = false 
  } = req.query;
  
  try {
    const releases = await updateService.getAllReleases({
      page: parseInt(page),
      limit: parseInt(limit),
      channel,
      includePrerelease: includePrerelease === 'true'
    });
    
    res.json({
      success: true,
      releases: releases.data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: releases.total,
        pages: Math.ceil(releases.total / limit)
      }
    });
  } catch (error) {
    logger.error('Error al obtener lista de releases:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener lista de versiones'
    });
  }
}));

// Obtener información detallada de una versión específica
router.get('/release/:version', asyncHandler(async (req, res) => {
  const { version } = req.params;
  
  if (!semver.valid(version)) {
    return res.status(400).json({
      success: false,
      message: 'Formato de versión inválido'
    });
  }
  
  try {
    const releaseInfo = await updateService.getReleaseInfo(version);
    
    if (!releaseInfo) {
      return res.status(404).json({
        success: false,
        message: 'Versión no encontrada'
      });
    }
    
    const downloadUrls = await downloadService.getDownloadUrls(version);
    const checksums = await downloadService.getFileChecksums(null, version);
    
    res.json({
      success: true,
      release: {
        ...releaseInfo,
        downloadUrls,
        checksums
      }
    });
  } catch (error) {
    logger.error(`Error al obtener información de v${version}:`, error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener información de la versión'
    });
  }
}));

// Reportar instalación exitosa
router.post('/install/report', asyncHandler(async (req, res) => {
  const { 
    version, 
    platform, 
    architecture, 
    installTime, 
    success = true,
    error 
  } = req.body;
  
  if (!version || !platform) {
    return res.status(400).json({
      success: false,
      message: 'Versión y plataforma son requeridas'
    });
  }
  
  try {
    await updateService.reportInstallation({
      version,
      platform,
      architecture,
      installTime,
      success,
      error,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    });
    
    logger.info(`Instalación reportada: v${version} en ${platform} - ${success ? 'exitosa' : 'fallida'}`);
    
    res.json({
      success: true,
      message: 'Reporte de instalación recibido'
    });
  } catch (error) {
    logger.error('Error al reportar instalación:', error);
    res.status(500).json({
      success: false,
      message: 'Error al procesar reporte'
    });
  }
}));

// Obtener estadísticas de actualizaciones (requiere autenticación)
router.get('/stats', asyncHandler(async (req, res) => {
  // TODO: Implementar autenticación más robusta
  
  try {
    const stats = await updateService.getUpdateStats();
    
    res.json({
      success: true,
      stats
    });
  } catch (error) {
    logger.error('Error al obtener estadísticas:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener estadísticas'
    });
  }
}));

// Endpoint de configuración del cliente
router.get('/config', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    config: {
      updateCheckInterval: config.updateCheckInterval,
      supportedPlatforms: ['windows', 'macos', 'linux'],
      channels: ['stable', 'beta', 'development'],
      apiVersion: '0.0.2'
    }
  });
}));

// ===== MÓDULOS API ENDPOINTS =====

// Obtener lista de módulos disponibles
router.get('/modules', asyncHandler(async (req, res) => {
  try {
    const modules = await moduleService.getAllModules();
    
    res.json({
      success: true,
      modules: modules.map(module => ({
        id: module.id,
        name: module.name,
        description: module.description,
        version: module.latestVersion,
        category: module.category,
        status: module.status,
        downloadable: module.downloadable,
        size: module.packageSize,
        downloads: module.downloads,
        compatibility: module.manifest.compatibility || {},
        features: module.features || [],
        downloadUrl: `${req.protocol}://${req.get('host')}/api/modules/${module.id}/download`,
        infoUrl: `${req.protocol}://${req.get('host')}/api/modules/${module.id}`
      }))
    });
  } catch (error) {
    logger.error('Error al obtener módulos via API:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener lista de módulos'
    });
  }
}));

// Obtener información de un módulo específico
router.get('/modules/:moduleId', asyncHandler(async (req, res) => {
  const { moduleId } = req.params;
  
  try {
    const moduleInfo = await moduleService.getModuleInfo(moduleId);
    
    if (!moduleInfo) {
      return res.status(404).json({
        success: false,
        message: 'Módulo no encontrado'
      });
    }
    
    const baseModule = moduleService.availableModules[moduleId] || {};
    
    res.json({
      success: true,
      module: {
        id: moduleId,
        name: baseModule.name || moduleId,
        description: baseModule.description || '',
        category: baseModule.category || 'general',
        features: baseModule.features || [],
        version: moduleInfo.latestVersion,
        versions: moduleInfo.versions,
        downloadable: moduleInfo.downloadable,
        status: moduleInfo.status,
        size: moduleInfo.packageSize,
        downloads: moduleInfo.downloads,
        manifest: moduleInfo.manifest,
        downloadUrl: `${req.protocol}://${req.get('host')}/api/modules/${moduleId}/download`,
        compatibility: moduleInfo.manifest.compatibility || {}
      }
    });
  } catch (error) {
    logger.error(`Error al obtener módulo ${moduleId} via API:`, error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener información del módulo'
    });
  }
}));

// Get module package for CoreDesk framework (JSON format)
router.get('/modules/:moduleId/:version/package', asyncHandler(async (req, res) => {
  const { moduleId, version } = req.params;
  
  logger.info(`API: Solicitud de paquete de módulo ${moduleId} v${version}`);
  
  try {
    const modulePackage = await moduleService.getModulePackage(moduleId, version === 'latest' ? null : version);
    
    if (!modulePackage) {
      return res.status(404).json({
        success: false,
        message: 'Módulo no encontrado o no disponible'
      });
    }
    
    // Return package in CoreDesk expected format
    res.json({
      success: true,
      ...modulePackage
    });
    
  } catch (error) {
    logger.error(`Error al obtener paquete de módulo ${moduleId}:`, error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener paquete del módulo'
    });
  }
}));

// Get module info (compatibility endpoint)
router.get('/modules/:moduleId/:version/info', asyncHandler(async (req, res) => {
  const { moduleId, version } = req.params;
  
  try {
    const moduleInfo = await moduleService.getModuleInfo(moduleId);
    
    if (!moduleInfo) {
      return res.status(404).json({
        success: false,
        message: 'Módulo no encontrado'
      });
    }
    
    // Return info with download URL in the format expected by CoreDesk
    res.json({
      manifest: moduleInfo.manifest || {
        id: moduleId,
        name: moduleInfo.name || moduleId,
        version: version === 'latest' ? moduleInfo.latestVersion : version,
        description: moduleInfo.description || ''
      },
      downloadUrl: `${req.protocol}://${req.get('host')}/api/modules/${moduleId}/${version}/package`,
      expectedChecksum: moduleInfo.manifest?.checksum || null
    });
    
  } catch (error) {
    logger.error(`Error al obtener info de módulo ${moduleId}:`, error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener información del módulo'
    });
  }
}));

// Descargar módulo via API
router.get('/modules/:moduleId/download', asyncHandler(async (req, res) => {
  const { moduleId } = req.params;
  const { version } = req.query;
  
  logger.info(`API: Solicitud de descarga módulo ${moduleId} v${version || 'latest'}`);
  
  try {
    const downloadInfo = await moduleService.getDownloadUrl(moduleId, version);
    
    if (!downloadInfo) {
      return res.status(404).json({
        success: false,
        message: 'Módulo no disponible para descarga'
      });
    }
    
    // Verificar compatibilidad
    const compatibility = await moduleService.verifyCompatibility(moduleId, version);
    if (!compatibility.compatible) {
      return res.status(400).json({
        success: false,
        message: `Módulo incompatible: ${compatibility.reason}`
      });
    }
    
    // Registrar descarga
    await moduleService.logDownload({
      moduleId,
      version: downloadInfo.version,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    });
    
    // Enviar archivo
    if (downloadInfo.type === 'local') {
      try {
        const fs = require('fs').promises;
        await fs.access(downloadInfo.path);
        res.download(downloadInfo.path, downloadInfo.filename, (err) => {
          if (err) {
            logger.error(`Error al enviar archivo ${downloadInfo.filename}:`, err);
            if (!res.headersSent) {
              res.status(500).json({
                success: false,
                message: 'Error al descargar el archivo'
              });
            }
          }
        });
      } catch (error) {
        logger.error(`Archivo no encontrado: ${downloadInfo.path}`);
        res.status(404).json({
          success: false,
          message: 'Archivo no disponible'
        });
      }
    } else {
      // Redirigir a URL externa
      res.redirect(downloadInfo.url);
    }
  } catch (error) {
    logger.error(`Error en descarga API ${moduleId}:`, error);
    res.status(500).json({
      success: false,
      message: 'Error al procesar la descarga'
    });
  }
}));

// Buscar módulos
router.get('/modules/search', asyncHandler(async (req, res) => {
  const { q: query, category } = req.query;
  
  try {
    const modules = await moduleService.searchModules(query, category);
    
    res.json({
      success: true,
      query: query || '',
      category: category || '',
      modules: modules.map(module => ({
        id: module.id,
        name: module.name,
        description: module.description,
        version: module.latestVersion,
        category: module.category,
        status: module.status,
        downloadable: module.downloadable,
        size: module.packageSize,
        downloads: module.downloads
      })),
      totalResults: modules.length
    });
  } catch (error) {
    logger.error('Error al buscar módulos via API:', error);
    res.status(500).json({
      success: false,
      message: 'Error al buscar módulos'
    });
  }
}));

module.exports = router;
